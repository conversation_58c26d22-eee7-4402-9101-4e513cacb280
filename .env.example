# Model Configuration
MODEL_PROVIDER=llamacpp
MODEL_NAME=llama
TEMPERATURE=0.7
MAX_TOKENS=2048

# LLaMA.cpp Configuration
LLAMACPP_API_URL=http://************:11111/completion

# Tool Configuration
# Available tools: search, math, web, rag
# Use comma-separated list to enable multiple tools
ENABLED_TOOLS=search,math,web,rag

# Agent Configuration
VERBOSE=false

# Container Configuration
DATA_DIR=./data
LOG_LEVEL=INFO

# Search Provider Configuration
# Options: duckduckgo (default, no API key required), google, bing
SEARCH_PROVIDER=duckduckgo

# Google Custom Search API (optional, for google search provider)
# Get these from: https://developers.google.com/custom-search/v1/overview
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Bing Search API (optional, for bing search provider)
# Get this from: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api
BING_SEARCH_API_KEY=your_bing_api_key_here

# OpenAI API (for future model provider support)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API (for future model provider support)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Memory Configuration
MEMORY_MAX_TOKENS=3500
MEMORY_DB_PATH=conversation_memory.db

# RAG Configuration
RAG_STORE_PATH=data/rag_store
RAG_EMBEDDING_MODEL=all-MiniLM-L6-v2
