#!/usr/bin/env python3
"""
Quick test script for agent tools with optimized settings.
"""
import os
import time
from dotenv import load_dotenv

def test_bitcoin_price():
    """Test getting Bitcoin price quickly."""
    print("🚀 Quick Bitcoin Price Test")
    
    # Load environment
    if os.path.exists('.env'):
        load_dotenv()
    
    try:
        from config.config import Config
        from agents.agent_factory import create_agent
        from models import get_model_provider
        from tools import get_available_tools
        from langchain.agents import AgentExecutor
        
        # Initialize components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        tools = get_available_tools(['web'])  # Only web tools
        llm = model_provider.get_llm()
        
        print(f"Available tools: {[tool.name for tool in tools]}")
        
        # Create optimized agent
        agent = create_agent(llm=llm, tools=tools, verbose=True)
        agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=2,  # Limit iterations
            max_execution_time=20,  # 20 second timeout
            early_stopping_method="generate"
        )
        
        # Test with a simple scraping request
        print("\n=== Testing Bitcoin Price Scraping ===")
        start_time = time.time()
        
        response = agent_executor.invoke({
            'input': 'Scrape https://coinmarketcap.com/currencies/bitcoin/ and tell me the Bitcoin price'
        })
        
        end_time = time.time()
        print(f"\n⏱️ Total time: {end_time - start_time:.2f} seconds")
        print(f"🤖 Agent response: {response['output']}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def test_direct_scraping():
    """Test direct scraping without agent."""
    print("\n🔧 Direct Scraping Test")
    
    try:
        from tools.web_tools import scrape_webpage
        import re
        
        start_time = time.time()
        result = scrape_webpage('https://coinmarketcap.com/currencies/bitcoin/', 2000)
        end_time = time.time()
        
        print(f"⏱️ Scraping time: {end_time - start_time:.2f} seconds")
        print(f"📄 Content length: {len(result)} characters")
        
        # Try to extract price from the content
        price_patterns = [
            r'\$[\d,]+\.?\d*',  # $123,456.78
            r'[\d,]+\.?\d*\s*USD',  # 123,456.78 USD
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, result)
            if matches:
                print(f"💰 Found potential prices: {matches[:5]}")
                break
        
        print(f"📝 Sample content: {result[:300]}...")
        
    except Exception as e:
        print(f"❌ Direct scraping error: {str(e)}")

if __name__ == "__main__":
    print("⚡ Quick Agent Performance Test\n")
    
    # Test direct scraping first
    test_direct_scraping()
    
    # Test with agent
    test_bitcoin_price()
    
    print("\n✅ Testing complete!")
    print("\n💡 Speed optimization tips:")
    print("- Reduced max_tokens to 512 for faster responses")
    print("- Set max_iterations=2 to prevent long loops")
    print("- Added 20-second timeout")
    print("- Use specific, direct prompts")
