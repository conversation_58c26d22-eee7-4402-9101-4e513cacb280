"""
RAG (Retrieval-Augmented Generation) tools for document search and analysis.
"""
import os
import json
import hashlib
from typing import List, Dict, Any, Optional
from pathlib import Path

from langchain.tools import BaseTool, StructuredTool
from langchain.tools.base import ToolException
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from tools.base import ToolProvider
from logger.get_logger import log

# Try to import optional dependencies
try:
    import faiss
    import numpy as np
    from sentence_transformers import SentenceTransformer
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False


class SimpleRAGStore:
    """Simple RAG document store using FAISS for vector search."""
    
    def __init__(self, store_path: str = "data/rag_store"):
        """Initialize the RAG store."""
        self.store_path = Path(store_path)
        self.store_path.mkdir(parents=True, exist_ok=True)
        
        self.documents_file = self.store_path / "documents.json"
        self.index_file = self.store_path / "faiss_index.bin"
        
        # Initialize components
        if FAISS_AVAILABLE:
            self.embeddings_model = SentenceTransformer('all-MiniLM-L6-v2')
            self.dimension = 384  # Dimension for all-MiniLM-L6-v2
        else:
            self.embeddings_model = None
            self.dimension = None
        
        self.documents = []
        self.index = None
        
        # Load existing data
        self._load_store()
    
    def _load_store(self):
        """Load existing documents and index."""
        # Load documents
        if self.documents_file.exists():
            with open(self.documents_file, 'r', encoding='utf-8') as f:
                self.documents = json.load(f)
        
        # Load FAISS index
        if FAISS_AVAILABLE and self.index_file.exists() and self.documents:
            try:
                self.index = faiss.read_index(str(self.index_file))
            except Exception as e:
                print(f"Warning: Could not load FAISS index: {e}")
                self.index = None
    
    def _save_store(self):
        """Save documents and index to disk."""
        # Save documents
        with open(self.documents_file, 'w', encoding='utf-8') as f:
            json.dump(self.documents, f, indent=2, ensure_ascii=False)
        
        # Save FAISS index
        if FAISS_AVAILABLE and self.index is not None:
            faiss.write_index(self.index, str(self.index_file))
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Add a document to the store."""
        if not FAISS_AVAILABLE:
            return "RAG functionality requires faiss-cpu and sentence-transformers. Please install them."
        
        if metadata is None:
            metadata = {}
        
        # Create document ID
        doc_id = hashlib.md5(content.encode()).hexdigest()
        
        # Check if document already exists
        for doc in self.documents:
            if doc['id'] == doc_id:
                return f"Document already exists with ID: {doc_id}"
        
        # Split document into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=50,
            length_function=len
        )
        chunks = text_splitter.split_text(content)
        
        # Generate embeddings for chunks
        embeddings = self.embeddings_model.encode(chunks)
        
        # Add to FAISS index
        if self.index is None:
            self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        self.index.add(embeddings.astype('float32'))
        
        # Store document metadata
        doc_data = {
            'id': doc_id,
            'content': content,
            'chunks': chunks,
            'metadata': metadata,
            'chunk_start_idx': len(self.documents) * len(chunks) if self.documents else 0
        }
        self.documents.append(doc_data)
        
        # Save to disk
        self._save_store()
        
        return f"Added document with ID: {doc_id} ({len(chunks)} chunks)"
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant documents."""
        if not FAISS_AVAILABLE or self.index is None or not self.documents:
            return []
        
        # Generate query embedding
        query_embedding = self.embeddings_model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # Search
        scores, indices = self.index.search(query_embedding.astype('float32'), top_k)
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx == -1:  # No more results
                break
            
            # Find which document this chunk belongs to
            chunk_idx = idx
            for doc in self.documents:
                doc_chunks = len(doc['chunks'])
                if chunk_idx < doc_chunks:
                    results.append({
                        'document_id': doc['id'],
                        'content': doc['chunks'][chunk_idx],
                        'full_content': doc['content'],
                        'metadata': doc['metadata'],
                        'score': float(score),
                        'chunk_index': chunk_idx
                    })
                    break
                chunk_idx -= doc_chunks
        
        return results
    
    def list_documents(self) -> List[Dict[str, Any]]:
        """List all documents in the store."""
        return [
            {
                'id': doc['id'],
                'metadata': doc['metadata'],
                'content_preview': doc['content'][:200] + '...' if len(doc['content']) > 200 else doc['content'],
                'chunks_count': len(doc['chunks'])
            }
            for doc in self.documents
        ]
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete a document from the store."""
        # Find and remove document
        for i, doc in enumerate(self.documents):
            if doc['id'] == doc_id:
                del self.documents[i]
                # Note: This doesn't remove from FAISS index, would need rebuilding
                self._save_store()
                return True
        return False


# Global RAG store instance
_rag_store = None

def get_rag_store() -> SimpleRAGStore:
    """Get the global RAG store instance."""
    global _rag_store
    if _rag_store is None:
        _rag_store = SimpleRAGStore()
    return _rag_store


@log
def add_document_to_rag(content: str, title: str = "", source: str = "") -> str:
    """
    Add a document to the RAG knowledge base.
    
    Args:
        content: The document content
        title: Optional title for the document
        source: Optional source information
        
    Returns:
        Status message about the operation
    """
    try:
        store = get_rag_store()
        metadata = {}
        if title:
            metadata['title'] = title
        if source:
            metadata['source'] = source
        
        result = store.add_document(content, metadata)
        return result
        
    except Exception as e:
        return f"Error adding document to RAG: {str(e)}"


@log
def search_rag_documents(query: str, top_k: int = 5) -> str:
    """
    Search the RAG knowledge base for relevant information.
    
    Args:
        query: The search query
        top_k: Number of top results to return (default: 5)
        
    Returns:
        Relevant document chunks formatted as a string
    """
    try:
        store = get_rag_store()
        results = store.search(query, top_k)
        
        if not results:
            return f"No relevant documents found for query: {query}"
        
        formatted_results = [f"RAG search results for: {query}\n"]
        
        for i, result in enumerate(results, 1):
            title = result['metadata'].get('title', 'Untitled')
            source = result['metadata'].get('source', 'Unknown source')
            score = result['score']
            content = result['content']
            
            formatted_results.append(f"{i}. {title} (Score: {score:.3f})")
            formatted_results.append(f"   Source: {source}")
            formatted_results.append(f"   Content: {content}")
            formatted_results.append("")
        
        return "\n".join(formatted_results)
        
    except Exception as e:
        return f"Error searching RAG documents: {str(e)}"


@log
def list_rag_documents() -> str:
    """
    List all documents in the RAG knowledge base.
    
    Returns:
        List of documents with metadata
    """
    try:
        store = get_rag_store()
        documents = store.list_documents()
        
        if not documents:
            return "No documents in the RAG knowledge base."
        
        formatted_docs = ["Documents in RAG knowledge base:\n"]
        
        for i, doc in enumerate(documents, 1):
            title = doc['metadata'].get('title', 'Untitled')
            source = doc['metadata'].get('source', 'Unknown source')
            chunks_count = doc['chunks_count']
            preview = doc['content_preview']
            
            formatted_docs.append(f"{i}. {title}")
            formatted_docs.append(f"   ID: {doc['id']}")
            formatted_docs.append(f"   Source: {source}")
            formatted_docs.append(f"   Chunks: {chunks_count}")
            formatted_docs.append(f"   Preview: {preview}")
            formatted_docs.append("")
        
        return "\n".join(formatted_docs)
        
    except Exception as e:
        return f"Error listing RAG documents: {str(e)}"


class RAGTools(ToolProvider):
    """RAG (Retrieval-Augmented Generation) tools provider."""

    @classmethod
    def get_tools(cls) -> List[BaseTool]:
        """Get the list of RAG tools."""
        return [
            StructuredTool.from_function(
                func=add_document_to_rag,
                name="add_document_to_rag",
                description="Add a document to the RAG knowledge base for future retrieval. Use this to store important information that you might need to reference later."
            ),
            StructuredTool.from_function(
                func=search_rag_documents,
                name="search_rag_documents",
                description="Search the RAG knowledge base for relevant information. Use this to find previously stored documents or information related to your query."
            ),
            StructuredTool.from_function(
                func=list_rag_documents,
                name="list_rag_documents",
                description="List all documents currently stored in the RAG knowledge base. Use this to see what information is available."
            )
        ]
