"""
Search tools for the AI agent.
"""
import os
import json
import requests
from typing import List, Dict, Any, Optional
from urllib.parse import quote_plus

from langchain.tools import BaseTool, StructuredTool
from langchain.tools.base import ToolException

from tools.base import ToolProvider
from logger.get_logger import log


class WebSearchProvider:
    """Base class for web search providers."""

    def search(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search the web and return results."""
        raise NotImplementedError


class DuckDuckGoSearchProvider(WebSearchProvider):
    """DuckDuckGo search provider (no API key required)."""

    def search(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search using DuckDuckGo Instant Answer API."""
        try:
            # DuckDuckGo Instant Answer API
            url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            results = []

            # Add abstract if available
            if data.get('Abstract'):
                results.append({
                    'title': data.get('AbstractSource', 'DuckDuckGo'),
                    'url': data.get('AbstractURL', ''),
                    'snippet': data.get('Abstract', '')
                })

            # Add related topics
            for topic in data.get('RelatedTopics', [])[:num_results-1]:
                if isinstance(topic, dict) and 'Text' in topic:
                    results.append({
                        'title': topic.get('FirstURL', '').split('/')[-1].replace('_', ' '),
                        'url': topic.get('FirstURL', ''),
                        'snippet': topic.get('Text', '')
                    })

            return results[:num_results]

        except Exception as e:
            raise ToolException(f"DuckDuckGo search failed: {str(e)}")


class GoogleSearchProvider(WebSearchProvider):
    """Google Custom Search API provider (requires API key)."""

    def __init__(self):
        self.api_key = os.getenv('GOOGLE_API_KEY')
        self.search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')

        if not self.api_key or not self.search_engine_id:
            raise ValueError("Google search requires GOOGLE_API_KEY and GOOGLE_SEARCH_ENGINE_ID environment variables")

    def search(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search using Google Custom Search API."""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': min(num_results, 10)  # Google API max is 10
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            results = []
            for item in data.get('items', []):
                results.append({
                    'title': item.get('title', ''),
                    'url': item.get('link', ''),
                    'snippet': item.get('snippet', '')
                })

            return results

        except Exception as e:
            raise ToolException(f"Google search failed: {str(e)}")


class BingSearchProvider(WebSearchProvider):
    """Bing Search API provider (requires API key)."""

    def __init__(self):
        self.api_key = os.getenv('BING_SEARCH_API_KEY')

        if not self.api_key:
            raise ValueError("Bing search requires BING_SEARCH_API_KEY environment variable")

    def search(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search using Bing Search API."""
        try:
            url = "https://api.bing.microsoft.com/v7.0/search"
            headers = {'Ocp-Apim-Subscription-Key': self.api_key}
            params = {
                'q': query,
                'count': min(num_results, 50),  # Bing API max is 50
                'responseFilter': 'Webpages'
            }

            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            results = []
            for item in data.get('webPages', {}).get('value', []):
                results.append({
                    'title': item.get('name', ''),
                    'url': item.get('url', ''),
                    'snippet': item.get('snippet', '')
                })

            return results

        except Exception as e:
            raise ToolException(f"Bing search failed: {str(e)}")


def get_search_provider() -> WebSearchProvider:
    """Get the configured search provider."""
    provider = os.getenv('SEARCH_PROVIDER', 'duckduckgo').lower()

    if provider == 'google':
        return GoogleSearchProvider()
    elif provider == 'bing':
        return BingSearchProvider()
    else:  # Default to DuckDuckGo
        return DuckDuckGoSearchProvider()


@log
def search_web(query: str, num_results: int = 5) -> str:
    """
    Search the web for information using the configured search provider.

    Args:
        query: The search query
        num_results: Number of results to return (default: 5)

    Returns:
        Search results formatted as a string
    """
    try:
        provider = get_search_provider()
        results = provider.search(query, num_results)

        if not results:
            return f"No search results found for: {query}"

        # Format results
        formatted_results = [f"Search results for: {query}\n"]
        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            url = result.get('url', 'No URL')
            snippet = result.get('snippet', 'No description available')

            formatted_results.append(f"{i}. {title}")
            formatted_results.append(f"   URL: {url}")
            formatted_results.append(f"   Description: {snippet}")
            formatted_results.append("")  # Empty line for readability

        return "\n".join(formatted_results)

    except Exception as e:
        return f"Search failed: {str(e)}"


@log
def search_news(query: str, num_results: int = 5) -> str:
    """
    Search for recent news articles.

    Args:
        query: The search query
        num_results: Number of results to return (default: 5)

    Returns:
        News search results formatted as a string
    """
    # Add "news" to the query to get more recent/news-focused results
    news_query = f"{query} news recent"
    return search_web(news_query, num_results)


class SearchTools(ToolProvider):
    """Search tools provider."""

    @classmethod
    def get_tools(cls) -> List[BaseTool]:
        """Get the list of search tools."""
        return [
            StructuredTool.from_function(
                func=search_web,
                name="search_web",
                description="Search the web for information. Use this when you need to find current information, facts, or answers that you don't already know. Provide a clear, specific search query."
            ),
            StructuredTool.from_function(
                func=search_news,
                name="search_news",
                description="Search for recent news articles about a topic. Use this when you need current news or recent developments about a subject."
            )
        ]
