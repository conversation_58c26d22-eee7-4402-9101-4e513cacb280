"""
Advanced web tools for the AI agent including web scraping and content analysis.
"""
import os
import requests
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
import re

from bs4 import BeautifulSoup
from langchain.tools import BaseTool, StructuredTool
from langchain.tools.base import ToolException

from tools.base import Too<PERSON><PERSON>rovider
from logger.get_logger import log


@log
def scrape_webpage(url: str, max_length: int = 5000) -> str:
    """
    Scrape and extract text content from a webpage.
    
    Args:
        url: The URL to scrape
        max_length: Maximum length of content to return (default: 5000)
        
    Returns:
        Extracted text content from the webpage
    """
    try:
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return f"Invalid URL: {url}"
        
        # Set headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        # Make request with timeout
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
            script.decompose()
        
        # Extract title
        title = soup.find('title')
        title_text = title.get_text().strip() if title else "No title"
        
        # Extract main content
        # Try to find main content areas
        main_content = None
        for selector in ['main', 'article', '.content', '#content', '.post', '.entry']:
            main_content = soup.select_one(selector)
            if main_content:
                break
        
        # If no main content found, use body
        if not main_content:
            main_content = soup.find('body')
        
        if not main_content:
            return f"Could not extract content from: {url}"
        
        # Extract text
        text = main_content.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Truncate if too long
        if len(text) > max_length:
            text = text[:max_length] + "... [Content truncated]"
        
        result = f"Title: {title_text}\nURL: {url}\n\nContent:\n{text}"
        return result
        
    except requests.exceptions.RequestException as e:
        return f"Failed to fetch webpage {url}: {str(e)}"
    except Exception as e:
        return f"Error processing webpage {url}: {str(e)}"


@log
def extract_links(url: str, max_links: int = 10) -> str:
    """
    Extract links from a webpage.
    
    Args:
        url: The URL to extract links from
        max_links: Maximum number of links to return (default: 10)
        
    Returns:
        List of links found on the webpage
    """
    try:
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return f"Invalid URL: {url}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all links
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().strip()
            
            # Convert relative URLs to absolute
            if href.startswith('/'):
                href = urljoin(url, href)
            elif not href.startswith(('http://', 'https://')):
                continue  # Skip non-HTTP links
            
            if text and href not in [l['url'] for l in links]:
                links.append({
                    'url': href,
                    'text': text[:100] + ('...' if len(text) > 100 else '')
                })
                
                if len(links) >= max_links:
                    break
        
        if not links:
            return f"No links found on: {url}"
        
        result = [f"Links found on {url}:\n"]
        for i, link in enumerate(links, 1):
            result.append(f"{i}. {link['text']}")
            result.append(f"   URL: {link['url']}")
            result.append("")
        
        return "\n".join(result)
        
    except Exception as e:
        return f"Error extracting links from {url}: {str(e)}"


@log
def search_and_scrape(query: str, num_results: int = 3) -> str:
    """
    Search the web and scrape content from the top results.
    
    Args:
        query: The search query
        num_results: Number of results to scrape (default: 3)
        
    Returns:
        Search results with scraped content
    """
    try:
        # Import the search function from search_tools
        from tools.search_tools import search_web, get_search_provider
        
        # Get search results
        provider = get_search_provider()
        results = provider.search(query, num_results)
        
        if not results:
            return f"No search results found for: {query}"
        
        scraped_results = [f"Search and scrape results for: {query}\n"]
        
        for i, result in enumerate(results, 1):
            url = result.get('url', '')
            title = result.get('title', 'No title')
            
            scraped_results.append(f"=== Result {i}: {title} ===")
            scraped_results.append(f"URL: {url}")
            
            if url:
                # Scrape the webpage content
                content = scrape_webpage(url, max_length=2000)  # Shorter for multiple results
                scraped_results.append(f"Content: {content}")
            else:
                scraped_results.append("Content: No URL available")
            
            scraped_results.append("\n" + "="*50 + "\n")
        
        return "\n".join(scraped_results)
        
    except Exception as e:
        return f"Error in search and scrape: {str(e)}"


class WebTools(ToolProvider):
    """Advanced web tools provider."""

    @classmethod
    def get_tools(cls) -> List[BaseTool]:
        """Get the list of web tools."""
        return [
            StructuredTool.from_function(
                func=scrape_webpage,
                name="scrape_webpage",
                description="Scrape and extract text content from a specific webpage URL. Use this when you have a specific URL and want to read its content."
            ),
            StructuredTool.from_function(
                func=extract_links,
                name="extract_links",
                description="Extract links from a webpage. Use this when you want to find related pages or resources from a specific webpage."
            ),
            StructuredTool.from_function(
                func=search_and_scrape,
                name="search_and_scrape",
                description="Search the web and automatically scrape content from the top results. Use this when you need comprehensive information about a topic from multiple sources."
            )
        ]
