"""
Tools package for the AI agent.
"""
from typing import Dict, List, Type

from langchain.tools import BaseTool

from tools.base import ToolProvider
from tools.search_tools import SearchTools
from tools.math_tools import MathTools
from tools.web_tools import WebTools
from tools.rag_tools import RAGTools

# Register all tool providers here
TOOL_PROVIDERS: Dict[str, Type[ToolProvider]] = {
    "search": SearchTools,
    "math": MathTools,
    "web": WebTools,
    "rag": RAGTools,
}

def get_available_tools(enabled_tools: List[str]) -> List[BaseTool]:
    """
    Get all available tools based on the enabled tool categories.
    
    Args:
        enabled_tools: List of enabled tool category names
        
    Returns:
        List of BaseTool instances
    """
    tools = []
    
    for tool_name in enabled_tools:
        if tool_name in TOOL_PROVIDERS:
            tools.extend(TOOL_PROVIDERS[tool_name].get_tools())
    
    return tools

__all__ = ["ToolProvider", "SearchTools", "MathTools", "get_available_tools"]
