"""
Agent Directives System - Automatic decision making for common requests.
"""
import re
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class AgentDirectives:
    """System to automatically determine what tools to use for different requests."""
    
    def __init__(self, directives_file: str = "agent_directives.json"):
        self.directives_file = Path(directives_file)
        self.directives = self._load_directives()
    
    def _load_directives(self) -> Dict:
        """Load directives from file or create default ones."""
        if self.directives_file.exists():
            with open(self.directives_file, 'r') as f:
                return json.load(f)
        else:
            # Create default directives
            default_directives = self._create_default_directives()
            self._save_directives(default_directives)
            return default_directives
    
    def _save_directives(self, directives: Dict):
        """Save directives to file."""
        with open(self.directives_file, 'w') as f:
            json.dump(directives, f, indent=2)
    
    def _create_default_directives(self) -> Dict:
        """Create default directive rules."""
        return {
            "price_queries": {
                "patterns": [
                    r"(?i).*(?:current|latest|today'?s?|now|real.?time).*(?:price|cost|value).*(?:bitcoin|btc)",
                    r"(?i).*(?:bitcoin|btc).*(?:current|latest|today'?s?|now|real.?time).*(?:price|cost|value)",
                    r"(?i).*(?:price|cost|value).*(?:bitcoin|btc).*(?:current|latest|today|now)",
                    r"(?i)what.*(?:bitcoin|btc).*(?:price|cost|trading)",
                    r"(?i)how much.*(?:bitcoin|btc).*(?:worth|cost)",
                    r"(?i)(?:bitcoin|btc).*(?:price|value).*(?:usd|dollar)"
                ],
                "action": {
                    "tool": "scrape_webpage",
                    "url": "https://coinmarketcap.com/currencies/bitcoin/",
                    "extract_pattern": r"\$[\d,]+\.?\d*",
                    "response_template": "The current Bitcoin price is {price} according to CoinMarketCap."
                }
            },
            "ethereum_price": {
                "patterns": [
                    r"(?i).*(?:current|latest|today'?s?|now).*(?:price|cost|value).*(?:ethereum|eth)",
                    r"(?i).*(?:ethereum|eth).*(?:current|latest|today'?s?|now).*(?:price|cost|value)",
                    r"(?i)what.*(?:ethereum|eth).*(?:price|cost|trading)"
                ],
                "action": {
                    "tool": "scrape_webpage", 
                    "url": "https://coinmarketcap.com/currencies/ethereum/",
                    "extract_pattern": r"\$[\d,]+\.?\d*",
                    "response_template": "The current Ethereum price is {price} according to CoinMarketCap."
                }
            },
            "calculations": {
                "patterns": [
                    r"(?i).*(?:calculate|compute|solve|find).*(?:\d+|\w+).*(?:\+|\-|\*|\/|sqrt|sin|cos|tan)",
                    r"(?i).*(?:what|how much).*(?:\d+.*\+|\d+.*\-|\d+.*\*|\d+.*\/)",
                    r"(?i).*(?:square root|sqrt).*(?:\d+)",
                    r"(?i).*(?:\d+).*(?:plus|minus|times|divided by).*(?:\d+)"
                ],
                "action": {
                    "tool": "calculate",
                    "extract_expression": True,
                    "response_template": "The calculation result is {result}."
                }
            },
            "web_search": {
                "patterns": [
                    r"(?i).*(?:search|find|look up|information about).*(?!.*(?:current|latest|price|cost))",
                    r"(?i).*(?:what is|tell me about|explain).*(?!.*(?:current|latest|price|cost))",
                    r"(?i).*(?:recent|latest).*(?:news|developments|updates)"
                ],
                "action": {
                    "tool": "search_web",
                    "response_template": "Here's what I found: {results}"
                }
            },
            "website_scraping": {
                "patterns": [
                    r"(?i).*(?:scrape|get content from|extract from).*(?:https?://|www\.)",
                    r"(?i).*(?:check|visit|look at).*(?:website|page|url).*(?:https?://|www\.)"
                ],
                "action": {
                    "tool": "scrape_webpage",
                    "extract_url": True,
                    "response_template": "Here's the content from the website: {content}"
                }
            }
        }
    
    def analyze_request(self, user_input: str) -> Optional[Tuple[str, Dict]]:
        """Analyze user input and return appropriate directive."""
        user_input = user_input.strip()
        
        for directive_name, directive in self.directives.items():
            for pattern in directive["patterns"]:
                if re.search(pattern, user_input):
                    return directive_name, directive["action"]
        
        return None
    
    def extract_calculation_expression(self, user_input: str) -> Optional[str]:
        """Extract mathematical expression from user input."""
        # Common calculation patterns
        patterns = [
            r"(\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*)",
            r"sqrt\s*\(\s*\d+(?:\.\d+)?\s*\)",
            r"sin\s*\(\s*\d+(?:\.\d+)?\s*\)",
            r"cos\s*\(\s*\d+(?:\.\d+)?\s*\)",
            r"(\d+(?:\.\d+)?)\s*(?:plus|\+)\s*(\d+(?:\.\d+)?)",
            r"(\d+(?:\.\d+)?)\s*(?:minus|\-)\s*(\d+(?:\.\d+)?)",
            r"(\d+(?:\.\d+)?)\s*(?:times|\*)\s*(\d+(?:\.\d+)?)",
            r"(\d+(?:\.\d+)?)\s*(?:divided by|\/)\s*(\d+(?:\.\d+)?)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def extract_url(self, user_input: str) -> Optional[str]:
        """Extract URL from user input."""
        url_pattern = r"https?://[^\s]+"
        match = re.search(url_pattern, user_input)
        if match:
            return match.group(0)
        return None
    
    def create_enhanced_prompt(self, user_input: str, directive_name: str, action: Dict) -> str:
        """Create an enhanced prompt that guides the agent to use the right tool."""
        
        if directive_name == "price_queries":
            return f"""The user is asking for current Bitcoin price. You MUST use the scrape_webpage tool to get real-time data.

User request: {user_input}

Instructions:
1. Use scrape_webpage tool with URL: {action['url']}
2. Extract the price from the scraped content
3. Provide the current price to the user

Remember: The user wants CURRENT price data, not general information."""

        elif directive_name == "calculations":
            expression = self.extract_calculation_expression(user_input)
            if expression:
                return f"""The user wants a mathematical calculation. You MUST use the calculate tool.

User request: {user_input}
Expression to calculate: {expression}

Instructions:
1. Use the calculate tool with the expression: {expression}
2. Provide the result to the user"""

        elif directive_name == "web_search":
            return f"""The user wants to search for information. Use the search_web tool.

User request: {user_input}

Instructions:
1. Use search_web tool to find relevant information
2. Summarize the findings for the user"""

        elif directive_name == "website_scraping":
            url = self.extract_url(user_input)
            if url:
                return f"""The user wants content from a specific website. Use the scrape_webpage tool.

User request: {user_input}
URL to scrape: {url}

Instructions:
1. Use scrape_webpage tool with URL: {url}
2. Extract and summarize the relevant content"""

        # Default enhanced prompt
        return f"""Analyze this user request and use the appropriate tool:

User request: {user_input}

Available tools: scrape_webpage, search_web, calculate, extract_links
Choose the most appropriate tool based on what the user is asking for."""


# Global instance
agent_directives = AgentDirectives()


def enhance_user_input(user_input: str) -> str:
    """Enhance user input with directive-based guidance."""
    result = agent_directives.analyze_request(user_input)
    
    if result:
        directive_name, action = result
        enhanced_prompt = agent_directives.create_enhanced_prompt(user_input, directive_name, action)
        return enhanced_prompt
    
    # If no specific directive matches, return original input
    return user_input


def get_directive_stats() -> Dict:
    """Get statistics about available directives."""
    directives = agent_directives.directives
    stats = {
        "total_directives": len(directives),
        "directive_types": list(directives.keys()),
        "total_patterns": sum(len(d["patterns"]) for d in directives.values())
    }
    return stats
