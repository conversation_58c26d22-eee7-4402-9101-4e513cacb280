"""
Main entry point for the AI agent application.
"""
import os
import signal
import sys
from dotenv import load_dotenv
from langchain.agents import AgentExecutor

from config.config import Config
from agents.agent_factory import create_agent
from models import get_model_provider
from tools import get_available_tools
from memory.manager import MemoryManager
from logger.get_logger import log, logger

# Set up signal handlers for graceful shutdown
def signal_handler(sig, frame):
    print("\nShutting down gracefully...")
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

@log
def main():
    """Main function to run the AI agent."""
    # Load environment variables from .env file if it exists
    if os.path.exists(".env"):
        load_dotenv()
        logger.info("Loaded environment variables from .env file")
    else:
        logger.info("No .env file found, using environment variables")

    # Load configuration
    config = Config()
    logger.info(f"Using model provider: {config.model_provider}")

    # Get model provider based on configuration
    try:
        model_provider = get_model_provider(config.model_provider)
        logger.info(f"Model provider initialized: {config.model_provider}")
    except Exception as e:
        logger.error(f"Failed to initialize model provider: {str(e)}")
        sys.exit(1)

    # Get available tools
    tools = get_available_tools(config.enabled_tools)
    logger.info(f"Enabled tools: {', '.join(config.enabled_tools)}")

    # Create agent
    try:
        agent = create_agent(
            llm=model_provider.get_llm(),
            tools=tools,
            verbose=config.verbose
        )
        logger.info("Agent created successfully")
    except Exception as e:
        logger.error(f"Failed to create agent: {str(e)}")
        sys.exit(1)

    # Initialize memory manager
    try:
        memory = MemoryManager(
            llm=model_provider.get_llm(),
            db_path="conversation_memory.db",
            max_tokens=3500  # Leave room for prompt and response within 4096 limit
        )
        logger.info("Memory manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize memory manager: {str(e)}")
        sys.exit(1)

    # Create agent executor with memory and optimized settings
    agent_executor = AgentExecutor.from_agent_and_tools(
        agent=agent,
        tools=tools,
        verbose=config.verbose,
        handle_parsing_errors=True,
        memory=memory,
        max_iterations=3,  # Limit iterations to prevent infinite loops
        max_execution_time=30  # 30 second timeout
    )

    # Run the agent in an interactive loop
    print("\nAI Agent initialized. Type 'exit' to quit.")
    print("Memory commands: !clear (clear memory), !stats (show memory statistics)")
    # Flush stdout to ensure logs are displayed before the prompt
    import sys
    sys.stdout.flush()

    while True:
        try:
            user_input = input("\nUser: ")

            # Handle special commands
            if user_input.lower() in ["exit", "quit"]:
                break
            elif user_input.lower() == "!clear":
                memory.clear()
                print("\nMemory cleared.")
                continue
            elif user_input.lower() == "!stats":
                stats = memory.get_stats()
                print("\nMemory statistics:")
                print(f"- Message count: {stats['message_count']}")
                print(f"- Topic count: {stats['topic_count']}")
                print(f"- Topics: {', '.join(stats['topics']) if stats['topics'] else 'None'}")
                continue

            response = agent_executor.invoke({"input": user_input})
            print(f"\nAgent: {response['output']}")
        except KeyboardInterrupt:
            print("\nShutting down...")
            break
        except Exception as e:
            logger.error(f"Error processing input: {str(e)}")
            print(f"\nError: {str(e)}")

    logger.info("AI Agent shutting down")

if __name__ == "__main__":
    main()
