#!/usr/bin/env python3
"""
Fast Bitcoin price getter - bypasses agent for speed.
"""
import re
import time
from tools.web_tools import scrape_webpage

def get_bitcoin_price():
    """Get current Bitcoin price quickly."""
    print("💰 Getting Bitcoin price...")
    
    start_time = time.time()
    
    # Scrape CoinMarketCap
    result = scrape_webpage('https://coinmarketcap.com/currencies/bitcoin/', 3000)
    
    # Extract price using regex
    price_patterns = [
        r'\$[\d,]+\.?\d*',  # $123,456.78
        r'[\d,]+\.?\d*\s*USD',  # 123,456.78 USD
    ]
    
    prices = []
    for pattern in price_patterns:
        matches = re.findall(pattern, result)
        prices.extend(matches)
    
    end_time = time.time()
    
    if prices:
        main_price = prices[0]  # First price is usually the main one
        print(f"🚀 Bitcoin Price: {main_price}")
        print(f"⏱️ Time taken: {end_time - start_time:.2f} seconds")
        return main_price
    else:
        print("❌ Could not extract price")
        print(f"Sample content: {result[:300]}...")
        return None

if __name__ == "__main__":
    get_bitcoin_price()
