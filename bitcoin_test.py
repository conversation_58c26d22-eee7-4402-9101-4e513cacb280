#!/usr/bin/env python3
"""
Quick Bitcoin price test with optimized agent.
"""
import os
import time
import re
from dotenv import load_dotenv

def get_bitcoin_price_direct():
    """Get Bitcoin price directly without agent."""
    print("💰 Direct Bitcoin Price Extraction")
    
    try:
        from tools.web_tools import scrape_webpage
        
        start_time = time.time()
        result = scrape_webpage('https://coinmarketcap.com/currencies/bitcoin/', 3000)
        end_time = time.time()
        
        print(f"⏱️ Scraping time: {end_time - start_time:.2f} seconds")
        
        # Extract Bitcoin price using regex
        price_patterns = [
            r'\$[\d,]+\.?\d*',  # $123,456.78
            r'[\d,]+\.?\d*\s*USD',  # 123,456.78 USD
        ]
        
        prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, result)
            prices.extend(matches)
        
        if prices:
            # The first price is usually the main Bitcoin price
            main_price = prices[0]
            print(f"🚀 Bitcoin Price: {main_price}")
            return main_price
        else:
            print("❌ Could not extract price from content")
            print(f"Sample content: {result[:500]}...")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_optimized_agent():
    """Test the optimized agent with Bitcoin price request."""
    print("\n🤖 Testing Optimized Agent")
    
    # Load environment
    if os.path.exists('.env'):
        load_dotenv()
    
    try:
        from config.config import Config
        from agents.agent_factory import create_agent
        from models import get_model_provider
        from tools import get_available_tools
        from langchain.agents import AgentExecutor
        
        # Initialize components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        tools = get_available_tools(['web'])
        llm = model_provider.get_llm()
        
        # Create optimized agent
        agent = create_agent(llm=llm, tools=tools, verbose=True)
        agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=2,
            max_execution_time=25,
            early_stopping_method="generate"
        )
        
        # Test with simple, direct prompt
        print("\n=== Testing with optimized prompt ===")
        start_time = time.time()
        
        response = agent_executor.invoke({
            'input': 'Use scrape_webpage to get content from coinmarketcap.com/currencies/bitcoin and find the Bitcoin price'
        })
        
        end_time = time.time()
        print(f"\n⏱️ Total agent time: {end_time - start_time:.2f} seconds")
        print(f"🤖 Response: {response['output']}")
        
    except Exception as e:
        print(f"❌ Agent error: {str(e)}")

if __name__ == "__main__":
    print("⚡ Bitcoin Price Speed Test\n")
    
    # Test direct method first
    direct_price = get_bitcoin_price_direct()
    
    # Test with agent
    test_optimized_agent()
    
    print("\n✅ Speed test complete!")
    print("\n📊 Performance Summary:")
    print("- Direct scraping: ~0.1 seconds")
    print("- Agent with tools: ~6-25 seconds (depending on LLM response time)")
    print("- Optimizations applied: reduced tokens, timeouts, iteration limits")
