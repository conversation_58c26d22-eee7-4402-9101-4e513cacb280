# Agent Tools Guide

This guide explains how to use and extend the various tools available in the dabot-agent application.

## Available Tool Categories

### 1. Math Tools (`math`)
Basic mathematical operations and calculations.

**Tools:**
- `calculate`: Evaluate mathematical expressions safely

**Example usage:**
```
User: What is 25 * 4 + sqrt(16)?
Agent: [Uses calculate tool] The result is 104.0
```

### 2. Search Tools (`search`)
Web search capabilities with multiple provider support.

**Tools:**
- `search_web`: General web search
- `search_news`: Search for recent news articles

**Supported Providers:**
- **DuckDuckGo** (default, no API key required)
- **Google Custom Search** (requires API key)
- **Bing Search** (requires API key)

**Configuration:**
```bash
# Use DuckDuckGo (default)
SEARCH_PROVIDER=duckduckgo

# Use Google (requires API keys)
SEARCH_PROVIDER=google
GOOGLE_API_KEY=your_api_key
GOOGLE_SEARCH_ENGINE_ID=your_engine_id

# Use Bing (requires API key)
SEARCH_PROVIDER=bing
BING_SEARCH_API_KEY=your_api_key
```

### 3. Web Tools (`web`)
Advanced web scraping and content analysis.

**Tools:**
- `scrape_webpage`: Extract text content from a specific URL
- `extract_links`: Get links from a webpage
- `search_and_scrape`: Search and automatically scrape top results

**Example usage:**
```
User: Scrape the content from https://example.com
Agent: [Uses scrape_webpage tool] [Returns formatted content]

User: Find and scrape information about Python programming
Agent: [Uses search_and_scrape tool] [Returns search results with scraped content]
```

### 4. RAG Tools (`rag`)
Retrieval-Augmented Generation for document storage and search.

**Tools:**
- `add_document_to_rag`: Store documents in the knowledge base
- `search_rag_documents`: Search stored documents
- `list_rag_documents`: List all stored documents

**Features:**
- Vector embeddings using sentence-transformers
- FAISS for efficient similarity search
- Document chunking for large texts
- Persistent storage

**Example usage:**
```
User: Add this document to the knowledge base: [document content]
Agent: [Uses add_document_to_rag] Document added with ID: abc123

User: Search for information about machine learning
Agent: [Uses search_rag_documents] [Returns relevant document chunks]
```

## Installation and Setup

### 1. Install Dependencies

```bash
# Install all dependencies
pip install -r requirements.txt

# For RAG functionality, ensure these are installed:
pip install faiss-cpu sentence-transformers
```

### 2. Configure Environment

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Edit `.env` to enable desired tools:

```bash
# Enable all tools
ENABLED_TOOLS=search,math,web,rag

# Or enable specific tools
ENABLED_TOOLS=search,math
```

### 3. Configure Search Providers (Optional)

For enhanced search capabilities:

**Google Custom Search:**
1. Go to [Google Custom Search](https://developers.google.com/custom-search/v1/overview)
2. Create a custom search engine
3. Get your API key and Search Engine ID
4. Add to `.env`:
   ```
   SEARCH_PROVIDER=google
   GOOGLE_API_KEY=your_api_key
   GOOGLE_SEARCH_ENGINE_ID=your_engine_id
   ```

**Bing Search:**
1. Go to [Bing Search API](https://www.microsoft.com/en-us/bing/apis/bing-web-search-api)
2. Get your API key
3. Add to `.env`:
   ```
   SEARCH_PROVIDER=bing
   BING_SEARCH_API_KEY=your_api_key
   ```

## Adding Custom Tools

### 1. Create a New Tool File

Create a new file in the `tools/` directory:

```python
# tools/my_custom_tools.py
from typing import List
from langchain.tools import BaseTool, StructuredTool
from tools.base import ToolProvider
from logger.get_logger import log

@log
def my_custom_function(input_param: str) -> str:
    """
    Description of what this tool does.
    
    Args:
        input_param: Description of the parameter
        
    Returns:
        Description of the return value
    """
    # Your tool implementation here
    return f"Processed: {input_param}"

class MyCustomTools(ToolProvider):
    """My custom tools provider."""

    @classmethod
    def get_tools(cls) -> List[BaseTool]:
        """Get the list of custom tools."""
        return [
            StructuredTool.from_function(
                func=my_custom_function,
                name="my_custom_tool",
                description="Description for the agent to understand when to use this tool"
            )
        ]
```

### 2. Register the Tool Provider

Add your tool provider to `tools/__init__.py`:

```python
from tools.my_custom_tools import MyCustomTools

TOOL_PROVIDERS: Dict[str, Type[ToolProvider]] = {
    "search": SearchTools,
    "math": MathTools,
    "web": WebTools,
    "rag": RAGTools,
    "custom": MyCustomTools,  # Add your tool here
}
```

### 3. Enable in Configuration

Add your tool category to the enabled tools:

```bash
ENABLED_TOOLS=search,math,web,rag,custom
```

## Best Practices

### Tool Design
1. **Clear descriptions**: Write clear tool descriptions so the agent knows when to use them
2. **Error handling**: Always include proper error handling in your tools
3. **Logging**: Use the `@log` decorator for debugging and monitoring
4. **Type hints**: Use proper type hints for better code quality

### Performance
1. **Caching**: Consider caching expensive operations
2. **Timeouts**: Set appropriate timeouts for network requests
3. **Rate limiting**: Respect API rate limits for external services

### Security
1. **Input validation**: Always validate and sanitize inputs
2. **API keys**: Store API keys in environment variables, never in code
3. **Sandboxing**: Be careful with tools that execute code or access files

## Troubleshooting

### Common Issues

1. **Tool not found**: Check that the tool is registered in `tools/__init__.py`
2. **Import errors**: Ensure all dependencies are installed
3. **API errors**: Verify API keys and network connectivity
4. **Memory issues**: For RAG tools, ensure sufficient disk space for embeddings

### Debug Mode

Enable verbose logging to see tool execution details:

```bash
VERBOSE=true
LOG_LEVEL=DEBUG
```

## Examples

### Complete Workflow Example

```
User: Research the latest developments in AI and save the information

Agent: I'll search for recent AI developments and save the information to our knowledge base.

[Uses search_web tool to find recent AI news]
[Uses scrape_webpage tool to get detailed content]
[Uses add_document_to_rag tool to store the information]

The information has been researched and saved. You can later search for it using queries about AI developments.
```

This demonstrates how multiple tools can work together to accomplish complex tasks.
