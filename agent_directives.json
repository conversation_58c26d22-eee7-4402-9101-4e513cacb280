{"price_queries": {"patterns": ["(?i).*(bitcoin|btc).*(price|cost|value|worth)", "(?i).*(price|cost|value|worth).*(bitcoin|btc)", "(?i)what.*(bitcoin|btc)", "(?i)how much.*(bitcoin|btc)", "(?i)(get|find|check).*(bitcoin|btc)", "(?i)(bitcoin|btc)\\s+(price|cost|value|worth|trading)", "(?i)(price|cost|value|worth)\\s+(bitcoin|btc)", "(?i)(bitcoin|btc)\\s*price", "(?i)price\\s*(bitcoin|btc)", "(?i)(current|latest|today|now).*(bitcoin|btc)"], "action": {"tool": "scrape_webpage", "url": "https://coinmarketcap.com/currencies/bitcoin/", "extract_pattern": "\\$[\\d,]+\\.?\\d*", "response_template": "The current Bitcoin price is {price} according to CoinMarketCap."}}, "ethereum_price": {"patterns": ["(?i).*(?:current|latest|today'?s?|now).*(?:price|cost|value).*(?:ethereum|eth)", "(?i).*(?:ethereum|eth).*(?:current|latest|today'?s?|now).*(?:price|cost|value)", "(?i)what.*(?:ethereum|eth).*(?:price|cost|trading)"], "action": {"tool": "scrape_webpage", "url": "https://coinmarketcap.com/currencies/ethereum/", "extract_pattern": "\\$[\\d,]+\\.?\\d*", "response_template": "The current Ethereum price is {price} according to CoinMarketCap."}}, "calculations": {"patterns": ["(?i).*(?:calculate|compute|solve|find).*(?:\\d+|\\w+).*(?:\\+|\\-|\\*|\\/|sqrt|sin|cos|tan)", "(?i).*(?:what|how much).*(?:\\d+.*\\+|\\d+.*\\-|\\d+.*\\*|\\d+.*\\/)", "(?i).*(?:square root|sqrt).*(?:\\d+)", "(?i).*(?:\\d+).*(?:plus|minus|times|divided by).*(?:\\d+)"], "action": {"tool": "calculate", "extract_expression": true, "response_template": "The calculation result is {result}."}}, "web_search": {"patterns": ["(?i).*(?:search|find|look up|information about).*(?!.*(?:current|latest|price|cost))", "(?i).*(?:what is|tell me about|explain).*(?!.*(?:current|latest|price|cost))", "(?i).*(?:recent|latest).*(?:news|developments|updates)"], "action": {"tool": "search_web", "response_template": "Here's what I found: {results}"}}, "website_scraping": {"patterns": ["(?i).*(?:scrape|get content from|extract from).*(?:https?://|www\\.)", "(?i).*(?:check|visit|look at).*(?:website|page|url).*(?:https?://|www\\.)"], "action": {"tool": "scrape_webpage", "extract_url": true, "response_template": "Here's the content from the website: {content}"}}}