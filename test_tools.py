#!/usr/bin/env python3
"""
Tool testing script to verify all agent tools are working correctly.
"""

def test_math_tools():
    """Test math calculation tools."""
    print("=== Testing Math Tools ===")
    from tools.math_tools import calculate
    
    try:
        result = calculate("sqrt(144) + 25")
        print(f"✅ calculate('sqrt(144) + 25') = {result}")
        
        result = calculate("2 * 3.14159 * 5")
        print(f"✅ calculate('2 * 3.14159 * 5') = {result}")
        
    except Exception as e:
        print(f"❌ Math tools error: {e}")

def test_search_tools():
    """Test web search tools."""
    print("\n=== Testing Search Tools ===")
    from tools.search_tools import search_web, search_news
    
    try:
        # Test general search
        result = search_web("Python programming", 2)
        if "Search results for:" in result:
            print("✅ search_web working")
            print(f"Sample result: {result[:200]}...")
        else:
            print(f"⚠️ search_web returned: {result}")
        
        # Test news search
        result = search_news("technology", 2)
        if "Search results for:" in result:
            print("✅ search_news working")
        else:
            print(f"⚠️ search_news returned: {result}")
            
    except Exception as e:
        print(f"❌ Search tools error: {e}")

def test_web_tools():
    """Test web scraping tools."""
    print("\n=== Testing Web Tools ===")
    from tools.web_tools import scrape_webpage, extract_links
    
    try:
        # Test web scraping
        result = scrape_webpage("https://httpbin.org/html", 500)
        if "Title:" in result:
            print("✅ scrape_webpage working")
            print(f"Sample result: {result[:200]}...")
        else:
            print(f"⚠️ scrape_webpage returned: {result}")
        
        # Test link extraction
        result = extract_links("https://httpbin.org/links/5", 3)
        if "Links found" in result:
            print("✅ extract_links working")
        else:
            print(f"⚠️ extract_links returned: {result}")
            
    except Exception as e:
        print(f"❌ Web tools error: {e}")

def test_agent_with_tools():
    """Test the full agent with tools."""
    print("\n=== Testing Full Agent ===")
    
    try:
        import os
        from dotenv import load_dotenv
        from config.config import Config
        from agents.agent_factory import create_agent
        from models import get_model_provider
        from tools import get_available_tools
        from langchain.agents import AgentExecutor
        
        # Load environment
        if os.path.exists('.env'):
            load_dotenv()
        
        # Initialize components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        tools = get_available_tools(['math'])  # Start with just math
        llm = model_provider.get_llm()
        
        # Create agent
        agent = create_agent(llm=llm, tools=tools, verbose=False)
        agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=2
        )
        
        # Test with a clear tool-requiring question
        print("Testing: 'Calculate the square root of 256 plus 44'")
        response = agent_executor.invoke({
            'input': 'I need you to use the calculate tool to find the square root of 256 plus 44'
        })
        print(f"Agent response: {response['output']}")
        
        if "calculate" in str(response).lower() or "20" in response['output']:
            print("✅ Agent is using tools correctly")
        else:
            print("⚠️ Agent may not be using tools - check prompt format")
            
    except Exception as e:
        print(f"❌ Agent test error: {e}")

if __name__ == "__main__":
    print("🔧 Testing Agent Tools\n")
    
    test_math_tools()
    test_search_tools() 
    test_web_tools()
    test_agent_with_tools()
    
    print("\n✅ Tool testing complete!")
    print("\n💡 Tips for using tools with your agent:")
    print("- Use phrases like 'I need current information about...'")
    print("- Say 'Please search for...' or 'Look up...'")
    print("- For calculations, say 'Calculate...' or 'I need you to compute...'")
    print("- Be specific about needing 'current', 'latest', or 'today's' information")
